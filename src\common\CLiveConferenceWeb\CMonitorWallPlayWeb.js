import Tool from "@/common/tool.js";
import CEvent from "@/common/CEvent";
import {Logger} from '@/common/console.js'
class CMonitorWallPlayWebLogger {
    constructor() {
        this.log = function ({ message, data }) {
            Logger.save({
                message,
                eventType: `monitor_wall_play_web_log`,
                data
            });
        };
        this.error = function ({ message, data }) {
            Logger.saveError({
                message,
                eventType: `monitor_wall_play_web_error`,
                data
            });
        };
    }
}
const logger = new CMonitorWallPlayWebLogger();
class CMonitorWallPlayWeb {
    constructor(option) {
        this.agoraClient = null;
        this.uid = option.uid;
        this.channelId = option.channelId;
        this.token = option.token;
        this.appId = option.appId;
        this.playDom = option.playDom;
        this.monitorWallRoom = option.monitorWallRoom
        this.joining = false;
        this.joined = false;
        this.remotePublishedUser = {};
        this.subscribeList = []
        this.event = new CEvent();
        this.initAgoraRTC();
    }

    initAgoraRTC() {
        let AgoraRTC = null;
        if (!AgoraRTC) {
            AgoraRTC = require("agora-rtc-sdk-ng"); // 这个没办法写在外面，会影响安卓端的上架。
        }
        this.agoraClient = AgoraRTC.createClient({ mode: "rtc", codec: 'vp8' });
        const {isLocal,localAgoraIP} = Tool.getAgoraProxyInfo()
        if(isLocal){
            // let t = JSON.parse(`{\"log\": {}, \"report\": {} , \"accessPoints\": {\"serverList\": [\"${localAgoraIP}\"], \"domain\": \"${ca_domain}"}}`);
            // this.agoraClient.setLocalAccessPointsV2(t);
            AgoraRTC.setParameter("JOIN_WITH_FALLBACK_SIGNAL_PROXY", false);
            AgoraRTC.setParameter("JOIN_WITH_FALLBACK_MEDIA_PROXY", false);
            AgoraRTC.setParameter('CONNECT_GATEWAY_WITHOUT_DOMAIN',true)
            AgoraRTC.setParameter("WEBCS_DOMAIN",[`${localAgoraIP}`]);
            AgoraRTC.setParameter("EVENT_REPORT_DOMAIN",`${localAgoraIP}:6443`);
            AgoraRTC.setParameter("LOG_UPLOAD_SERVER",[`${localAgoraIP}:6444`]);
        }
        this.onAgoraEvent();
    }

    onAgoraEvent() {
        this.offAgoraEvent();
        this.agoraClient.on('token-privilege-will-expire', () => {
            this.NotifyTokenPrivilegeWillExpireMonitor();
        });
        this.agoraClient.on('token-privilege-did-expire', () => {
            this.NotifyTokenExpiredMonitor();
        });
        this.agoraClient.on("user-published", (user, mediaType) => {
            this.handleUserPublished(user, mediaType);
        });
        this.agoraClient.on("user-unpublished", (user, mediaType) => {
            this.handleUserUnPublished(user, mediaType);
        });
        this.agoraClient.on("user-joined", (user) => {
            this.handleUserJoined(user);
        });
        this.agoraClient.on("user-left", (user) => {
            this.handleUserLeft(user);
        });
    }

    offAgoraEvent() {
        if(this.agoraClient){
            this.agoraClient.off('token-privilege-will-expire');
            this.agoraClient.off('token-privilege-did-expire');
            this.agoraClient.off('user-published');
            this.agoraClient.off('user-unpublished');
            this.agoraClient.off('user-joined');
            this.agoraClient.off('user-left');
        }

    }

    JoinChannelMonitor() {
        return new Promise(async (resolve, reject) => {
            if (this.joining) {
                return reject(false);
            }
            this.joining = true;
            try {
                logger.log({message:'joinRoom',data:{channelId:this.channelId, uid:this.uid,monitorWallRoom:this.monitorWallRoom}})
                await this.agoraClient.join(this.appId, this.channelId, this.token, this.uid);
                this.joining = false;
                this.joined = true;
                this.ServiceReportJoinChannel(1)
                resolve(true);
            } catch (error) {
                // window.vm.$root.platformToast(error);
                this.ServiceReportJoinChannel(0)
                this.clearStatus();
                this.joining = false;
                reject(error);
            } finally {
                this.emitDataChange();
            }
        });
    }

    async LeaveChannelMonitor() {
        this.ServiceReportLeaveChannel(1)
        try {
            await this.agoraClient.leave();
        } catch (error) {
            logger.error({message:'LeaveChannelMonitor',data:error})
        }finally{
            this.clearStatus();
        }

    }

    async RenewTokenMonitor(newToken) {
        this.agoraClient.renewToken(newToken);
    }

    NotifyTokenExpiredMonitor() {
        this.LeaveChannelMonitor();
    }

    async NotifyTokenPrivilegeWillExpireMonitor() {
        const res = await this.ServiceGetConferenceRefreshToken({
            channelId: this.channelId,
            uid: this.uid
        });
        this.RenewTokenMonitor(res.data.token);
    }

    handleUserPublished(user, mediaType) {
        logger.log({message:'handleUserPublished'})
        if (mediaType === "video") {
            // 使用number类型的uid
            const uid = user.uid;
            this.remotePublishedUser[uid] = user;
            this.event.emit('handleUserPublished',{ uid: uid, channelId: this.channelId })
        }

    }

    handleUserUnPublished(user, mediaType) {
        logger.log({message:'handleUserUnPublished'})
        if (mediaType === "video") {
            // 使用number类型的uid
            const uid = user.uid;
            this.event.emit('handleUserUnPublished',{ uid: uid, channelId: this.channelId })
            delete this.remotePublishedUser[uid];
            this.StopSubscribeRemoteStreamMonitor(uid);
        }
    }

    handleUserJoined(user) {
        logger.log({message:'handleUserJoined'})
        // 使用number类型的uid
        const uid = user.uid;
        this.event.emit('handleUserJoined', { uid: uid, channelId: this.channelId });
    }

    handleUserLeft(user) {
        logger.log({message:'handleUserLeft'})
        // 使用number类型的uid
        const uid = user.uid;
        this.event.emit('handleUserLeft', { uid: uid, channelId: this.channelId });
    }

    async ServiceGetConferenceRefreshToken(data) {
        return new Promise((resolve, reject) => {
            window.main_screen.getConferenceRefreshToken(data, (res) => {
                if (res.error_code === 0 && res.data) {
                    resolve(res);
                } else {
                    reject(res);
                }
            });
        });
    }

    async SubscribeRemoteStreamMonitor(uid) {
        if (!uid) {
            return;
        }
        // 使用number类型的uid
        uid = Number(uid);

        const playDom = `${this.playDom}${uid}_${this.channelId}`;
        if(!document.querySelector(`#${playDom}`)){
            return
        }
        let user = this.remotePublishedUser[uid];
        if (!user) {
            if (!this.joined) {
                await this.waitChannelJoin();
                user = this.remotePublishedUser[uid];
                if (!user) {
                    return;
                }
            } else {
                return;
            }
        }

        try {
            if(this.subscribeList.includes(uid)){
                return
            }
            await this.agoraClient.subscribe(user, "video");
            this.subscribeList.push(uid)
            setTimeout(() => {
                if(!document.getElementById(playDom)){
                    return
                }
                user.videoTrack.play(playDom, { fit: "contain" });

                // 监听视频元素可见性状态变化事件（Agora SDK 4.23.0+）
                this.setupVideoVisibilityListener(user.videoTrack, uid);
            }, 0);
        } catch (error) {
            logger.error({message:'SubscribeRemoteStreamMonitor',data:error})
            setTimeout(() => {
                this.StopSubscribeRemoteStreamMonitor(uid);
            });
        }
    }

    async StopSubscribeRemoteStreamMonitor(uid) {
        // 使用number类型的uid
        uid = Number(uid);

        let user = this.remotePublishedUser[uid];
        if (!user) {
            return;
        }

        try {
            if(!this.subscribeList.includes(uid)){
                return
            }

            // 清理视频可见性监听器
            if (user.videoTrack && typeof user.videoTrack.off === 'function') {
                user.videoTrack.off("video-element-visible-status");
            }

            await this.agoraClient.unsubscribe(user, "video");
            this.subscribeList = this.subscribeList.filter(item=>item!==uid)
        } catch (error) {
            logger.error({message:'StopSubscribeRemoteStreamMonitor',data:error})
        }
    }

    async SetRemoteVideoStreamType({ uid, streamType }) {
        const streamTypeMap = {
            large: 0,
            small: 1
        };
        await this.agoraClient.setRemoteVideoStreamType(uid, streamTypeMap[streamType]);
    }
    stopUnVisibleSubscribe(){
        // 清理subscribeList中的重复项，统一转换为number类型
        this.cleanupSubscribeList();

        this.subscribeList.forEach(uid=>{
            const playDomStr = `${this.playDom}${uid}_${this.channelId}`;
            const playDom =  document.querySelector(`#${playDomStr}`)
            if(!playDom || !Tool.checkIsElementVisible(playDom)){
                console.error('stopUnVisibleSubscribe',uid)
                this.StopSubscribeRemoteStreamMonitor(uid)
            }
        })
    }
    checkVisibleSubscribe(){
        // 清理subscribeList中的重复项，统一转换为number类型
        this.cleanupSubscribeList();

        Object.keys(this.remotePublishedUser).forEach(uidKey=>{
            // Object.keys返回的是字符串，需要转换为number类型
            const uid = Number(uidKey);
            const playDomStr = `${this.playDom}${uid}_${this.channelId}`;
            const playDom =  document.querySelector(`#${playDomStr}`)

            if(playDom && Tool.checkIsElementVisible(playDom)){
                if(!this.subscribeList.includes(uid)){
                    this.SubscribeRemoteStreamMonitor(uid)
                } else {
                    // 如果已经订阅但元素重新可见，检查是否需要恢复播放
                    const user = this.remotePublishedUser[uid];
                    if (user && user.videoTrack && !this.isVideoAlreadyPlaying(playDom)) {
                        this.resumeVideoPlayback(user.videoTrack, uid);
                    }
                }
            }
        })
    }

    // 清理subscribeList中的重复项，统一转换为number类型
    cleanupSubscribeList() {
        // 将所有项转换为number并去重
        const uniqueList = [...new Set(this.subscribeList.map(uid => Number(uid)))];
        this.subscribeList = uniqueList;
    }

    /**
     * 设置视频可见性监听器，处理DOM元素隐藏显示时的视频播放恢复
     * @param {IRemoteVideoTrack} videoTrack - 远端视频轨道
     * @param {number} uid - 用户ID
     */
    setupVideoVisibilityListener(videoTrack, uid) {
        if (!videoTrack || typeof videoTrack.on !== 'function') {
            return;
        }

        try {
            // 监听视频元素可见性状态变化事件（Agora SDK 4.23.0+）
            videoTrack.on("video-element-visible-status", (data) => {
                logger.log({
                    message: 'video-element-visible-status',
                    data: { uid, visible: data.visible, reason: data.reason }
                });

                if (data.visible) {
                    // 当视频元素重新可见时，尝试恢复播放
                    this.resumeVideoPlayback(videoTrack, uid);
                } else {
                    logger.log({
                        message: 'video element hidden',
                        data: { uid, reason: data.reason }
                    });
                }
            });
        } catch (error) {
            logger.error({
                message: 'setupVideoVisibilityListener error',
                data: { uid, error: error.message }
            });
        }
    }

    /**
     * 恢复视频播放
     * @param {IRemoteVideoTrack} videoTrack - 远端视频轨道
     * @param {number} uid - 用户ID
     */
    resumeVideoPlayback(videoTrack, uid) {
        try {
            const playDom = `${this.playDom}${uid}_${this.channelId}`;
            const domElement = document.getElementById(playDom);

            if (!domElement) {
                logger.error({
                    message: 'resumeVideoPlayback: DOM element not found',
                    data: { uid, playDom }
                });
                return;
            }

            // 检查DOM元素是否真的可见
            if (!Tool.checkIsElementVisible(domElement)) {
                logger.log({
                    message: 'resumeVideoPlayback: DOM element still not visible',
                    data: { uid }
                });
                return;
            }

            // 检查视频是否已经在播放
            if (this.isVideoAlreadyPlaying(domElement)) {
                logger.log({
                    message: 'resumeVideoPlayback: video already playing, skip resume',
                    data: { uid, playDom }
                });
                return;
            }

            // 重新播放视频到指定DOM元素
            videoTrack.play(playDom, { fit: "contain" });

            logger.log({
                message: 'video playback resumed successfully',
                data: { uid, playDom }
            });
        } catch (error) {
            logger.error({
                message: 'resumeVideoPlayback error',
                data: { uid, error: error.message }
            });
        }
    }

    /**
     * 检查视频是否已经在播放
     * @param {HTMLElement|string} domElementOrId - DOM元素或元素ID
     * @returns {boolean} 是否正在播放
     */
    isVideoAlreadyPlaying(domElementOrId) {
        try {
            let domElement;
            if (typeof domElementOrId === 'string') {
                domElement = document.getElementById(domElementOrId);
            } else {
                domElement = domElementOrId;
            }

            if (!domElement) {
                return false;
            }

            // 查找DOM元素内的video标签
            const videoElement = domElement.querySelector('video');
            if (!videoElement) {
                return false;
            }

            // 检查video元素的播放状态
            // 如果video元素存在且不是暂停状态，并且有视频数据，则认为正在播放
            const isPlaying = !videoElement.paused &&
                             videoElement.readyState >= 2 && // HAVE_CURRENT_DATA
                             videoElement.currentTime > 0;

            // 额外检查：如果video元素的尺寸大于0，说明有视频内容在渲染
            const hasVideoContent = videoElement.videoWidth > 0 && videoElement.videoHeight > 0;

            return isPlaying && hasVideoContent;
        } catch (error) {
            logger.error({
                message: 'isVideoAlreadyPlaying error',
                data: { error: error.message }
            });
            return false;
        }
    }
    clearStatus() {
        // 清理所有视频可见性监听器
        Object.keys(this.remotePublishedUser).forEach(uid => {
            const user = this.remotePublishedUser[uid];
            if (user && user.videoTrack && typeof user.videoTrack.off === 'function') {
                user.videoTrack.off("video-element-visible-status");
            }
        });

        this.uid = 0;
        this.channelId = 0;
        this.token = '';
        this.appId = '';
        this.playDom = null;
        this.monitorWallRoom = null
        this.joining = false;
        this.joined = false;
        this.remotePublishedUser = {};
        this.subscribeList = []
        this.emitDataChange();
        this.offAgoraEvent();
        this.agoraClient = null
    }

    emitDataChange() {
        this.event.emit("monitor_play_data_change", {
            joined: this.joined,
            joining: this.joining
        });
    }

    waitChannelJoin() {
        return new Promise((resolve) => {
            if (this.joined) {
                resolve(true);
            } else {
                this.event.on("monitor_play_data_change", (data) => {
                    if (data.joined) {
                        setTimeout(() => {
                            resolve(true);
                        }, 300);
                    }
                });
            }
        });
    }
    ServiceReportJoinChannel(status){
        return new Promise((resolve,reject)=>{
            this.monitorWallRoom&&this.monitorWallRoom.api.reportUserJoinChannel({
                channelId: this.channelId,
                clientSeq:new Date().getTime(),
                uid:this.uid,
                status
            },
            (res) => {
                logger.log({message:'reportUserJoinChannel',data:res})
                if (res.is_success) {
                    resolve(res);
                } else {
                    reject(res);
                }
            })
        })
    }
    ServiceReportLeaveChannel(status){
        return new Promise((resolve,reject)=>{
            this.monitorWallRoom&&this.monitorWallRoom.api.reportUserLeaveChannel({
                channelId: this.channelId,
                clientSeq:new Date().getTime(),
                uid:this.uid,
                status
            },
            (res) => {
                logger.log({message:'reportUserLeaveChannel',data:res})
                if (res.error_code === 0) {
                    resolve(res);
                } else {
                    reject(res);
                }
            })
        })
    }
}

export default CMonitorWallPlayWeb;
